from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

# from database.connection import init_db
from mqtt.client import mqtt_client
from routers import devices, tanks, pumps, mqtt as mqtt_router, websocket

# Load environment variables
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    # await init_db()
    print("Connecting to MQTT broker...")
    await mqtt_client.connect()
    yield
    # Shutdown
    await mqtt_client.disconnect()

app = FastAPI(
    title="Smarteye Device MQTT Service",
    description="IoT backend service for managing Pumps and Tanks using MQTT, FastAPI, MySQL, Redis, and Mosquitto",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
# app.include_router(devices.router, prefix="/api/v1/devices", tags=["devices"])
# app.include_router(tanks.router, prefix="/api/v1/tanks", tags=["tanks"])
# app.include_router(pumps.router, prefix="/api/v1/pumps", tags=["pumps"])
# app.include_router(mqtt_router.router, prefix="/api/v1/mqtt", tags=["mqtt"])
# app.include_router(websocket.router, prefix="/ws", tags=["websocket"])


@app.get("/")
async def root():
    return {"message": "Smarteye Device MQTT Service", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
